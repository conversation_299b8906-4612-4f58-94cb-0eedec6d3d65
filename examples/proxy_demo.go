package main

import (
	"fmt"
	"log"
	"time"

	"crawler/pkg/proxy"
	"crawler/pkg/proxy/vmess"
)

func main() {
	// 创建代理管理器
	pm := proxy.NewProxyManager()
	
	fmt.Println("=== 代理接口系统演示 ===")
	
	// 演示1: 手动添加测试节点（模拟从订阅链接解析的结果）
	fmt.Println("\n1. 添加测试节点...")
	testNodes := createTestNodes()
	
	// 模拟导入订阅的结果
	pm.ImportTestNodes(testNodes)
	
	fmt.Printf("成功添加 %d 个节点\n", pm.GetNodeCount())
	
	// 演示2: 显示所有节点
	fmt.Println("\n2. 显示所有节点:")
	nodes := pm.GetAllNodes()
	for i, node := range nodes {
		fmt.Printf("  [%d] %s\n", i+1, node.String())
	}
	
	// 演示3: 执行健康检查
	fmt.Println("\n3. 执行健康检查...")
	pm.BatchHealthCheck()
	
	// 等待健康检查完成
	time.Sleep(2 * time.Second)
	
	fmt.Printf("健康节点数量: %d/%d\n", pm.GetHealthyNodeCount(), pm.GetNodeCount())
	
	// 演示4: 获取随机代理
	fmt.Println("\n4. 获取随机代理:")
	for i := 0; i < 3; i++ {
		node, err := pm.GetRandomProxy()
		if err != nil {
			log.Printf("获取随机代理失败: %v", err)
			continue
		}
		fmt.Printf("  随机代理 %d: %s\n", i+1, node.String())
	}
	
	// 演示5: 显示健康节点
	fmt.Println("\n5. 显示健康节点:")
	healthyNodes := pm.GetHealthyNodes()
	for i, node := range healthyNodes {
		fmt.Printf("  [%d] %s (响应时间: %v)\n", i+1, node.String(), node.ResponseTime)
	}
	
	// 演示6: 移除不健康节点
	fmt.Println("\n6. 移除不健康节点...")
	removedCount := pm.RemoveUnhealthyNodes(3) // 连续失败3次以上的节点将被移除
	fmt.Printf("移除了 %d 个不健康节点\n", removedCount)
	fmt.Printf("剩余节点数量: %d\n", pm.GetNodeCount())
	
	// 演示7: 模拟订阅链接导入（注释掉，因为需要真实的订阅链接）
	fmt.Println("\n7. 订阅链接导入演示:")
	fmt.Println("  // 示例代码:")
	fmt.Println("  // err := pm.ImportSubscription(\"https://example.com/subscription\")")
	fmt.Println("  // if err != nil {")
	fmt.Println("  //     log.Printf(\"导入订阅失败: %v\", err)")
	fmt.Println("  // }")
	
	fmt.Println("\n=== 演示完成 ===")
}

// createTestNodes 创建测试节点
func createTestNodes() []*vmess.VmessNode {
	return []*vmess.VmessNode{
		{
			ID:              "test1:443",
			Name:            "香港节点-01",
			Host:            "hk1.example.com",
			Port:            443,
			UUID:            "12345678-1234-1234-1234-123456789abc",
			AlterID:         0,
			Security:        "auto",
			Network:         "ws",
			Type:            "none",
			Path:            "/path",
			TLS:             "tls",
			IsHealthyStatus: false,
			FailCount:       0,
		},
		{
			ID:              "test2:80",
			Name:            "美国节点-01",
			Host:            "us1.example.com",
			Port:            80,
			UUID:            "*************-4321-4321-cba987654321",
			AlterID:         0,
			Security:        "auto",
			Network:         "tcp",
			Type:            "none",
			Path:            "",
			TLS:             "",
			IsHealthyStatus: false,
			FailCount:       0,
		},
		{
			ID:              "test3:8080",
			Name:            "日本节点-01",
			Host:            "jp1.example.com",
			Port:            8080,
			UUID:            "11111111-**************-************",
			AlterID:         0,
			Security:        "auto",
			Network:         "ws",
			Type:            "none",
			Path:            "/ws",
			TLS:             "",
			IsHealthyStatus: false,
			FailCount:       0,
		},
		{
			ID:              "localhost:22",
			Name:            "本地测试节点",
			Host:            "127.0.0.1",
			Port:            22, // SSH端口，通常是开放的
			UUID:            "00000000-0000-0000-0000-000000000000",
			AlterID:         0,
			Security:        "auto",
			Network:         "tcp",
			Type:            "none",
			Path:            "",
			TLS:             "",
			IsHealthyStatus: false,
			FailCount:       0,
		},
	}
}

// 注意：ImportTestNodes 方法已经在 pkg/proxy/enter.go 中实现

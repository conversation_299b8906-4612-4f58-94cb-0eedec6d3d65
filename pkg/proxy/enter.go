package proxy

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"
)

// VmessNode 代表一个vmess代理节点
type VmessNode struct {
	ID       string `json:"id"`       // 节点唯一标识
	Name     string `json:"name"`     // 节点名称
	Host     string `json:"host"`     // 服务器地址
	Port     int    `json:"port"`     // 端口号
	UUID     string `json:"uuid"`     // 用户ID
	AlterID  int    `json:"alter_id"` // 额外ID
	Security string `json:"security"` // 加密方式
	Network  string `json:"network"`  // 传输协议
	Type     string `json:"type"`     // 伪装类型
	Path     string `json:"path"`     // 路径
	TLS      string `json:"tls"`      // TLS设置

	// 健康检查相关
	IsHealthy    bool          `json:"is_healthy"`    // 是否健康
	LastCheck    time.Time     `json:"last_check"`    // 最后检查时间
	ResponseTime time.Duration `json:"response_time"` // 响应时间
	FailCount    int           `json:"fail_count"`    // 连续失败次数
}

// ProxyManager 代理管理器
type ProxyManager struct {
	nodes       []*VmessNode    // 代理节点列表
	mutex       sync.RWMutex    // 读写锁
	httpClient  *http.Client    // HTTP客户端
	healthCheck bool            // 是否启用健康检查
}

// SSRProxy 保持原有接口兼容性
type SSRProxy interface {
}

// ProxyInterface 代理接口定义
type ProxyInterface interface {
	// ImportSubscription 导入机场订阅链接
	ImportSubscription(subscriptionURL string) error

	// GetRandomProxy 获取随机可用代理
	GetRandomProxy() (*VmessNode, error)

	// HealthCheck 执行节点健康检查
	HealthCheck(node *VmessNode) error

	// GetAllNodes 获取所有节点
	GetAllNodes() []*VmessNode

	// GetHealthyNodes 获取所有健康节点
	GetHealthyNodes() []*VmessNode
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager() *ProxyManager {
	return &ProxyManager{
		nodes:       make([]*VmessNode, 0),
		mutex:       sync.RWMutex{},
		healthCheck: true,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
	}
}

// ImportSubscription 导入机场订阅链接
func (pm *ProxyManager) ImportSubscription(subscriptionURL string) error {
	// 获取订阅内容
	resp, err := pm.httpClient.Get(subscriptionURL)
	if err != nil {
		return fmt.Errorf("获取订阅链接失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("订阅链接返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取订阅内容失败: %v", err)
	}

	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		return fmt.Errorf("Base64解码失败: %v", err)
	}

	// 按行分割配置
	lines := strings.Split(string(decodedData), "\n")
	var newNodes []*VmessNode

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 解析vmess配置
		node, err := pm.parseVmessConfig(line)
		if err != nil {
			// 记录解析失败的配置，但不中断整个导入过程
			fmt.Printf("解析配置失败: %s, 错误: %v\n", line, err)
			continue
		}

		if node != nil {
			newNodes = append(newNodes, node)
		}
	}

	// 更新节点列表
	pm.mutex.Lock()
	pm.nodes = newNodes
	pm.mutex.Unlock()

	return nil
}

// parseVmessConfig 解析vmess配置字符串
func (pm *ProxyManager) parseVmessConfig(configLine string) (*VmessNode, error) {
	configLine = strings.TrimSpace(configLine)

	// 检查是否为vmess协议
	if !strings.HasPrefix(configLine, "vmess://") {
		return nil, fmt.Errorf("不支持的协议类型")
	}

	// 移除协议前缀
	configData := strings.TrimPrefix(configLine, "vmess://")

	// 尝试不同的解析格式
	// 1. 尝试V2RayN格式 (Base64编码的JSON)
	if node, err := pm.parseV2RayNFormat(configData); err == nil {
		return node, nil
	}

	// 2. 尝试ShadowRocket格式
	if node, err := pm.parseShadowRocketFormat(configData); err == nil {
		return node, nil
	}

	// 3. 尝试Quantumult格式
	if node, err := pm.parseQuantumultFormat(configData); err == nil {
		return node, nil
	}

	return nil, fmt.Errorf("无法解析vmess配置格式")
}

// parseV2RayNFormat 解析V2RayN格式: vmess://(Base64编码的json格式服务器数据)
func (pm *ProxyManager) parseV2RayNFormat(configData string) (*VmessNode, error) {
	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(configData)
	if err != nil {
		return nil, fmt.Errorf("V2RayN格式Base64解码失败: %v", err)
	}

	// 定义V2RayN配置结构
	var v2rayConfig struct {
		V    string `json:"v"`    // 版本
		PS   string `json:"ps"`   // 备注名称
		Add  string `json:"add"`  // 服务器地址
		Port string `json:"port"` // 端口
		ID   string `json:"id"`   // UUID
		Aid  string `json:"aid"`  // 额外ID
		Net  string `json:"net"`  // 传输协议
		Type string `json:"type"` // 伪装类型
		Host string `json:"host"` // 伪装域名
		Path string `json:"path"` // 路径
		TLS  string `json:"tls"`  // TLS
	}

	// JSON解析
	if err := json.Unmarshal(decodedData, &v2rayConfig); err != nil {
		return nil, fmt.Errorf("V2RayN格式JSON解析失败: %v", err)
	}

	// 转换端口为整数
	port, err := strconv.Atoi(v2rayConfig.Port)
	if err != nil {
		return nil, fmt.Errorf("端口格式错误: %v", err)
	}

	// 转换额外ID为整数
	alterID, err := strconv.Atoi(v2rayConfig.Aid)
	if err != nil {
		alterID = 0 // 默认值
	}

	// 创建节点
	node := &VmessNode{
		ID:       generateNodeID(v2rayConfig.Add, port),
		Name:     v2rayConfig.PS,
		Host:     v2rayConfig.Add,
		Port:     port,
		UUID:     v2rayConfig.ID,
		AlterID:  alterID,
		Security: "auto", // V2RayN格式通常使用auto
		Network:  v2rayConfig.Net,
		Type:     v2rayConfig.Type,
		Path:     v2rayConfig.Path,
		TLS:      v2rayConfig.TLS,
		IsHealthy: false, // 初始状态为未检查
		LastCheck: time.Time{},
		FailCount: 0,
	}

	return node, nil
}

// generateNodeID 生成节点唯一标识
func generateNodeID(host string, port int) string {
	return fmt.Sprintf("%s:%d", host, port)
}

// parseShadowRocketFormat 解析ShadowRocket格式: vmess://base64([cipher]:[uuid]@[host]:[port])?remarks=queryEscape([name])
func (pm *ProxyManager) parseShadowRocketFormat(configData string) (*VmessNode, error) {
	// 检查是否包含查询参数
	parts := strings.Split(configData, "?")
	if len(parts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式不正确")
	}

	// Base64解码主要部分
	decodedData, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式Base64解码失败: %v", err)
	}

	// 解析主要部分: [cipher]:[uuid]@[host]:[port]
	configStr := string(decodedData)
	atIndex := strings.LastIndex(configStr, "@")
	if atIndex == -1 {
		return nil, fmt.Errorf("ShadowRocket格式解析失败: 缺少@符号")
	}

	// 分离认证信息和服务器信息
	authPart := configStr[:atIndex]
	serverPart := configStr[atIndex+1:]

	// 解析认证信息: [cipher]:[uuid]
	authParts := strings.Split(authPart, ":")
	if len(authParts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式认证信息不正确")
	}
	cipher := authParts[0]
	uuid := authParts[1]

	// 解析服务器信息: [host]:[port]
	serverParts := strings.Split(serverPart, ":")
	if len(serverParts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式服务器信息不正确")
	}
	host := serverParts[0]
	port, err := strconv.Atoi(serverParts[1])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式端口解析失败: %v", err)
	}

	// 解析查询参数
	queryParams, err := url.ParseQuery(parts[1])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式查询参数解析失败: %v", err)
	}

	name := queryParams.Get("remarks")
	if name == "" {
		name = fmt.Sprintf("%s:%d", host, port)
	}

	// 创建节点
	node := &VmessNode{
		ID:       generateNodeID(host, port),
		Name:     name,
		Host:     host,
		Port:     port,
		UUID:     uuid,
		AlterID:  0,
		Security: cipher,
		Network:  "tcp", // ShadowRocket格式默认使用tcp
		Type:     "none",
		Path:     "",
		TLS:      "",
		IsHealthy: false,
		LastCheck: time.Time{},
		FailCount: 0,
	}

	return node, nil
}

// parseQuantumultFormat 解析Quantumult格式: vmess=[host]:[port], method=[method], password=[uuid], tag=[tag]
func (pm *ProxyManager) parseQuantumultFormat(configData string) (*VmessNode, error) {
	// Quantumult格式不是以vmess://开头，而是直接的配置字符串
	// 这里我们假设传入的configData已经去掉了vmess://前缀

	// 检查是否包含必要的字段
	if !strings.Contains(configData, "vmess=") {
		return nil, fmt.Errorf("不是Quantumult格式")
	}

	// 解析配置参数
	params := make(map[string]string)
	parts := strings.Split(configData, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "=") {
			kv := strings.SplitN(part, "=", 2)
			if len(kv) == 2 {
				key := strings.TrimSpace(kv[0])
				value := strings.TrimSpace(kv[1])
				params[key] = value
			}
		}
	}

	// 解析服务器地址和端口
	serverInfo := params["vmess"]
	if serverInfo == "" {
		return nil, fmt.Errorf("Quantumult格式缺少服务器信息")
	}

	serverParts := strings.Split(serverInfo, ":")
	if len(serverParts) != 2 {
		return nil, fmt.Errorf("Quantumult格式服务器信息不正确")
	}

	host := serverParts[0]
	port, err := strconv.Atoi(serverParts[1])
	if err != nil {
		return nil, fmt.Errorf("Quantumult格式端口解析失败: %v", err)
	}

	// 获取其他参数
	uuid := params["password"]
	if uuid == "" {
		return nil, fmt.Errorf("Quantumult格式缺少密码")
	}

	method := params["method"]
	if method == "" {
		method = "auto"
	}

	tag := params["tag"]
	if tag == "" {
		tag = fmt.Sprintf("%s:%d", host, port)
	}

	// 创建节点
	node := &VmessNode{
		ID:       generateNodeID(host, port),
		Name:     tag,
		Host:     host,
		Port:     port,
		UUID:     uuid,
		AlterID:  0,
		Security: method,
		Network:  "tcp", // Quantumult格式默认使用tcp
		Type:     "none",
		Path:     "",
		TLS:      "",
		IsHealthy: false,
		LastCheck: time.Time{},
		FailCount: 0,
	}

	return node, nil
}

// GetRandomProxy 获取随机可用代理
func (pm *ProxyManager) GetRandomProxy() (*VmessNode, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.nodes) == 0 {
		return nil, fmt.Errorf("没有可用的代理节点")
	}

	// 优先选择健康的节点
	healthyNodes := make([]*VmessNode, 0)
	for _, node := range pm.nodes {
		if node.IsHealthy {
			healthyNodes = append(healthyNodes, node)
		}
	}

	// 如果有健康节点，从中随机选择
	if len(healthyNodes) > 0 {
		randomIndex := rand.Intn(len(healthyNodes))
		return healthyNodes[randomIndex], nil
	}

	// 如果没有健康节点，从所有节点中随机选择
	randomIndex := rand.Intn(len(pm.nodes))
	return pm.nodes[randomIndex], nil
}

// HealthCheck 执行节点健康检查
func (pm *ProxyManager) HealthCheck(node *VmessNode) error {
	if node == nil {
		return fmt.Errorf("节点不能为空")
	}

	startTime := time.Now()

	// 尝试连接到代理服务器
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", node.Host, node.Port), 5*time.Second)
	if err != nil {
		// 连接失败
		pm.mutex.Lock()
		node.IsHealthy = false
		node.LastCheck = time.Now()
		node.FailCount++
		pm.mutex.Unlock()
		return fmt.Errorf("连接失败: %v", err)
	}
	defer conn.Close()

	// 计算响应时间
	responseTime := time.Since(startTime)

	// 更新节点状态
	pm.mutex.Lock()
	node.IsHealthy = true
	node.LastCheck = time.Now()
	node.ResponseTime = responseTime
	node.FailCount = 0
	pm.mutex.Unlock()

	return nil
}

// GetAllNodes 获取所有节点
func (pm *ProxyManager) GetAllNodes() []*VmessNode {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 返回节点副本，避免外部修改
	nodes := make([]*VmessNode, len(pm.nodes))
	copy(nodes, pm.nodes)
	return nodes
}

// GetHealthyNodes 获取所有健康节点
func (pm *ProxyManager) GetHealthyNodes() []*VmessNode {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	healthyNodes := make([]*VmessNode, 0)
	for _, node := range pm.nodes {
		if node.IsHealthy {
			healthyNodes = append(healthyNodes, node)
		}
	}
	return healthyNodes
}

// BatchHealthCheck 批量健康检查所有节点
func (pm *ProxyManager) BatchHealthCheck() {
	pm.mutex.RLock()
	nodes := make([]*VmessNode, len(pm.nodes))
	copy(nodes, pm.nodes)
	pm.mutex.RUnlock()

	// 并发检查所有节点
	for _, node := range nodes {
		go func(n *VmessNode) {
			pm.HealthCheck(n)
		}(node)
	}
}

// GetNodeCount 获取节点总数
func (pm *ProxyManager) GetNodeCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.nodes)
}

// GetHealthyNodeCount 获取健康节点数量
func (pm *ProxyManager) GetHealthyNodeCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	count := 0
	for _, node := range pm.nodes {
		if node.IsHealthy {
			count++
		}
	}
	return count
}

// RemoveUnhealthyNodes 移除不健康的节点（连续失败次数超过阈值）
func (pm *ProxyManager) RemoveUnhealthyNodes(maxFailCount int) int {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	var healthyNodes []*VmessNode
	removedCount := 0

	for _, node := range pm.nodes {
		if node.FailCount < maxFailCount {
			healthyNodes = append(healthyNodes, node)
		} else {
			removedCount++
		}
	}

	pm.nodes = healthyNodes
	return removedCount
}

// String 返回节点的字符串表示
func (node *VmessNode) String() string {
	status := "不健康"
	if node.IsHealthy {
		status = "健康"
	}
	uuidDisplay := node.UUID
	if len(node.UUID) > 8 {
		uuidDisplay = node.UUID[:8] + "..."
	}
	return fmt.Sprintf("节点[%s] %s:%d (%s) - %s",
		node.Name, node.Host, node.Port, uuidDisplay, status)
}

// ImportTestNodes 导入测试节点（仅用于测试和演示）
func (pm *ProxyManager) ImportTestNodes(nodes []*VmessNode) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.nodes = nodes
}

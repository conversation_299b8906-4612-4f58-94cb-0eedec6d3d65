package proxy

import (
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"crawler/pkg/proxy/vmess"
)

// ProxyManager 代理管理器
type ProxyManager struct {
	nodes           []*vmess.VmessNode     // 代理节点列表
	mutex           sync.RWMutex           // 读写锁
	httpClient      *http.Client           // HTTP客户端
	healthCheck     bool                   // 是否启用健康检查
	vmessParser     *vmess.VmessParser     // vmess协议解析器
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager() *ProxyManager {
	return &ProxyManager{
		nodes:       make([]*vmess.VmessNode, 0),
		mutex:       sync.RWMutex{},
		healthCheck: true,
		vmessParser: vmess.NewVmessParser(),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
	}
}

// GetSupportedProtocols 获取支持的协议列表
func (pm *ProxyManager) GetSupportedProtocols() []string {
	return []string{"vmess"}
}

// GetSupportedFormats 获取vmess支持的格式列表
func (pm *ProxyManager) GetSupportedFormats() []string {
	return pm.vmessParser.GetSupportedFormats()
}

// ImportSubscription 导入机场订阅链接
func (pm *ProxyManager) ImportSubscription(subscriptionURL string) error {
	// 获取订阅内容
	resp, err := pm.httpClient.Get(subscriptionURL)
	if err != nil {
		return fmt.Errorf("获取订阅链接失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("订阅链接返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取订阅内容失败: %v", err)
	}

	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		return fmt.Errorf("Base64解码失败: %v", err)
	}

	// 按行分割配置
	lines := strings.Split(string(decodedData), "\n")
	var newNodes []*vmess.VmessNode

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 使用协议解析器解析配置
		node, err := pm.parseProxyConfig(line)
		if err != nil {
			// 记录解析失败的配置，但不中断整个导入过程
			fmt.Printf("解析配置失败: %s, 错误: %v\n", line, err)
			continue
		}

		if node != nil {
			newNodes = append(newNodes, node)
		}
	}

	// 更新节点列表
	pm.mutex.Lock()
	pm.nodes = newNodes
	pm.mutex.Unlock()

	return nil
}

// parseProxyConfig 解析代理配置字符串
func (pm *ProxyManager) parseProxyConfig(configLine string) (*vmess.VmessNode, error) {
	configLine = strings.TrimSpace(configLine)

	if configLine == "" {
		return nil, fmt.Errorf("配置行为空")
	}

	// 使用vmess解析器解析配置
	if pm.vmessParser.CanParse(configLine) {
		return pm.vmessParser.Parse(configLine)
	}

	return nil, fmt.Errorf("不支持的协议类型")
}

// GetRandomProxy 获取随机可用代理
func (pm *ProxyManager) GetRandomProxy() (*vmess.VmessNode, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.nodes) == 0 {
		return nil, fmt.Errorf("没有可用的代理节点")
	}

	// 优先选择健康的节点
	healthyNodes := make([]*vmess.VmessNode, 0)
	for _, node := range pm.nodes {
		if node.IsHealthyStatus {
			healthyNodes = append(healthyNodes, node)
		}
	}

	// 如果有健康节点，从中随机选择
	if len(healthyNodes) > 0 {
		randomIndex := rand.Intn(len(healthyNodes))
		return healthyNodes[randomIndex], nil
	}

	// 如果没有健康节点，从所有节点中随机选择
	randomIndex := rand.Intn(len(pm.nodes))
	return pm.nodes[randomIndex], nil
}

// HealthCheck 执行节点健康检查
func (pm *ProxyManager) HealthCheck(node *vmess.VmessNode) error {
	if node == nil {
		return fmt.Errorf("节点不能为空")
	}

	startTime := time.Now()

	// 尝试连接到代理服务器
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", node.Host, node.Port), 5*time.Second)
	if err != nil {
		// 连接失败
		pm.mutex.Lock()
		node.IsHealthyStatus = false
		node.LastCheck = time.Now()
		node.FailCount++
		pm.mutex.Unlock()
		return fmt.Errorf("连接失败: %v", err)
	}
	defer conn.Close()

	// 计算响应时间
	responseTime := time.Since(startTime)

	// 更新节点状态
	pm.mutex.Lock()
	node.IsHealthyStatus = true
	node.LastCheck = time.Now()
	node.ResponseTime = responseTime
	node.FailCount = 0
	pm.mutex.Unlock()

	return nil
}

// GetAllNodes 获取所有节点
func (pm *ProxyManager) GetAllNodes() []*vmess.VmessNode {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 返回节点副本，避免外部修改
	nodes := make([]*vmess.VmessNode, len(pm.nodes))
	copy(nodes, pm.nodes)
	return nodes
}

// GetHealthyNodes 获取所有健康节点
func (pm *ProxyManager) GetHealthyNodes() []*vmess.VmessNode {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	healthyNodes := make([]*vmess.VmessNode, 0)
	for _, node := range pm.nodes {
		if node.IsHealthyStatus {
			healthyNodes = append(healthyNodes, node)
		}
	}
	return healthyNodes
}

// BatchHealthCheck 批量健康检查所有节点
func (pm *ProxyManager) BatchHealthCheck() {
	pm.mutex.RLock()
	nodes := make([]*vmess.VmessNode, len(pm.nodes))
	copy(nodes, pm.nodes)
	pm.mutex.RUnlock()

	// 并发检查所有节点
	for _, node := range nodes {
		go func(n *vmess.VmessNode) {
			pm.HealthCheck(n)
		}(node)
	}
}

// GetNodeCount 获取节点总数
func (pm *ProxyManager) GetNodeCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.nodes)
}

// GetHealthyNodeCount 获取健康节点数量
func (pm *ProxyManager) GetHealthyNodeCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	count := 0
	for _, node := range pm.nodes {
		if node.IsHealthyStatus {
			count++
		}
	}
	return count
}

// RemoveUnhealthyNodes 移除不健康的节点（连续失败次数超过阈值）
func (pm *ProxyManager) RemoveUnhealthyNodes(maxFailCount int) int {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	var healthyNodes []*vmess.VmessNode
	removedCount := 0

	for _, node := range pm.nodes {
		if node.FailCount < maxFailCount {
			healthyNodes = append(healthyNodes, node)
		} else {
			removedCount++
		}
	}

	pm.nodes = healthyNodes
	return removedCount
}

// ImportTestNodes 导入测试节点（仅用于测试和演示）
func (pm *ProxyManager) ImportTestNodes(nodes []*vmess.VmessNode) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.nodes = nodes
}

package proxy

import (
	"crypto/tls"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"crawler/pkg/proxy/vmess"
)

// ProxyManager 代理管理器
type ProxyManager struct {
	nodes           []*VmessNode           // 代理节点列表
	mutex           sync.RWMutex           // 读写锁
	httpClient      *http.Client           // HTTP客户端
	healthCheck     bool                   // 是否启用健康检查
	protocolParsers map[string]ProtocolParser // 协议解析器映射
}

// NewProxyManager 创建新的代理管理器
func NewProxyManager() *ProxyManager {
	pm := &ProxyManager{
		nodes:           make([]*VmessNode, 0),
		mutex:           sync.RWMutex{},
		healthCheck:     true,
		protocolParsers: make(map[string]ProtocolParser),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
	}
	
	// 注册默认的协议解析器
	pm.RegisterProtocolParser(vmess.NewVmessParser())
	
	return pm
}

// RegisterProtocolParser 注册协议解析器
func (pm *ProxyManager) RegisterProtocolParser(parser ProtocolParser) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.protocolParsers[parser.GetProtocol()] = parser
}

// GetSupportedProtocols 获取支持的协议列表
func (pm *ProxyManager) GetSupportedProtocols() []string {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	protocols := make([]string, 0, len(pm.protocolParsers))
	for protocol := range pm.protocolParsers {
		protocols = append(protocols, protocol)
	}
	return protocols
}

// ImportSubscription 导入机场订阅链接
func (pm *ProxyManager) ImportSubscription(subscriptionURL string) error {
	// 获取订阅内容
	resp, err := pm.httpClient.Get(subscriptionURL)
	if err != nil {
		return fmt.Errorf("获取订阅链接失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("订阅链接返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取订阅内容失败: %v", err)
	}

	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(string(body))
	if err != nil {
		return fmt.Errorf("Base64解码失败: %v", err)
	}

	// 按行分割配置
	lines := strings.Split(string(decodedData), "\n")
	var newNodes []*VmessNode

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 使用协议解析器解析配置
		node, err := pm.parseProxyConfig(line)
		if err != nil {
			// 记录解析失败的配置，但不中断整个导入过程
			fmt.Printf("解析配置失败: %s, 错误: %v\n", line, err)
			continue
		}

		if node != nil {
			newNodes = append(newNodes, node)
		}
	}

	// 更新节点列表
	pm.mutex.Lock()
	pm.nodes = newNodes
	pm.mutex.Unlock()

	return nil
}

// parseProxyConfig 解析代理配置字符串
func (pm *ProxyManager) parseProxyConfig(configLine string) (*VmessNode, error) {
	configLine = strings.TrimSpace(configLine)
	
	if configLine == "" {
		return nil, fmt.Errorf("配置行为空")
	}
	
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	
	// 尝试使用不同的协议解析器
	var lastErr error
	for _, parser := range pm.protocolParsers {
		if parser.CanParse(configLine) {
			node, err := parser.Parse(configLine)
			if err == nil {
				// 类型断言，确保返回的是VmessNode
				if vmessNode, ok := node.(*VmessNode); ok {
					return vmessNode, nil
				}
				return nil, fmt.Errorf("解析器返回了不支持的节点类型")
			}
			lastErr = err
		}
	}
	
	if lastErr != nil {
		return nil, fmt.Errorf("无法解析代理配置: %v", lastErr)
	}
	
	return nil, fmt.Errorf("没有找到支持的协议解析器")
}

// GetRandomProxy 获取随机可用代理
func (pm *ProxyManager) GetRandomProxy() (*VmessNode, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.nodes) == 0 {
		return nil, fmt.Errorf("没有可用的代理节点")
	}

	// 优先选择健康的节点
	healthyNodes := make([]*VmessNode, 0)
	for _, node := range pm.nodes {
		if node.IsHealthyStatus {
			healthyNodes = append(healthyNodes, node)
		}
	}

	// 如果有健康节点，从中随机选择
	if len(healthyNodes) > 0 {
		randomIndex := rand.Intn(len(healthyNodes))
		return healthyNodes[randomIndex], nil
	}

	// 如果没有健康节点，从所有节点中随机选择
	randomIndex := rand.Intn(len(pm.nodes))
	return pm.nodes[randomIndex], nil
}

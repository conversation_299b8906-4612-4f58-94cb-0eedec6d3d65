package proxy

import (
	"crawler/pkg/proxy/vmess"
)

// ProxyInterface 代理接口定义
type ProxyInterface interface {
	// ImportSubscription 导入机场订阅链接
	ImportSubscription(subscriptionURL string) error

	// GetRandomProxy 获取随机可用代理
	GetRandomProxy() (*vmess.VmessNode, error)

	// HealthCheck 执行节点健康检查
	HealthCheck(node *vmess.VmessNode) error

	// GetAllNodes 获取所有节点
	GetAllNodes() []*vmess.VmessNode

	// GetHealthyNodes 获取所有健康节点
	GetHealthyNodes() []*vmess.VmessNode
}

// SSRProxy 保持原有接口兼容性
type SSRProxy interface {
}

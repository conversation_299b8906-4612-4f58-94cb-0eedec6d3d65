package proxy

import (
	"fmt"
	"time"
)

// ProxyNode 通用代理节点接口，支持不同协议的节点
type ProxyNode interface {
	// GetID 获取节点唯一标识
	GetID() string
	
	// GetName 获取节点名称
	GetName() string
	
	// GetHost 获取服务器地址
	GetHost() string
	
	// GetPort 获取端口号
	GetPort() int
	
	// GetProtocol 获取协议类型
	GetProtocol() string
	
	// IsHealthy 检查节点是否健康
	IsHealthy() bool
	
	// GetLastCheck 获取最后检查时间
	GetLastCheck() time.Time
	
	// GetResponseTime 获取响应时间
	GetResponseTime() time.Duration
	
	// GetFailCount 获取连续失败次数
	GetFailCount() int
	
	// String 返回节点的字符串表示
	String() string
}

// VmessNode 代表一个vmess代理节点
type VmessNode struct {
	ID       string `json:"id"`       // 节点唯一标识
	Name     string `json:"name"`     // 节点名称
	Host     string `json:"host"`     // 服务器地址
	Port     int    `json:"port"`     // 端口号
	UUID     string `json:"uuid"`     // 用户ID
	AlterID  int    `json:"alter_id"` // 额外ID
	Security string `json:"security"` // 加密方式
	Network  string `json:"network"`  // 传输协议
	Type     string `json:"type"`     // 伪装类型
	Path     string `json:"path"`     // 路径
	TLS      string `json:"tls"`      // TLS设置
	
	// 健康检查相关
	IsHealthyStatus bool          `json:"is_healthy"`    // 是否健康
	LastCheck       time.Time     `json:"last_check"`    // 最后检查时间
	ResponseTime    time.Duration `json:"response_time"` // 响应时间
	FailCount       int           `json:"fail_count"`    // 连续失败次数
}

// 实现ProxyNode接口
func (node *VmessNode) GetID() string {
	return node.ID
}

func (node *VmessNode) GetName() string {
	return node.Name
}

func (node *VmessNode) GetHost() string {
	return node.Host
}

func (node *VmessNode) GetPort() int {
	return node.Port
}

func (node *VmessNode) GetProtocol() string {
	return "vmess"
}

func (node *VmessNode) IsHealthy() bool {
	return node.IsHealthyStatus
}

func (node *VmessNode) GetLastCheck() time.Time {
	return node.LastCheck
}

func (node *VmessNode) GetResponseTime() time.Duration {
	return node.ResponseTime
}

func (node *VmessNode) GetFailCount() int {
	return node.FailCount
}

// ProtocolParser 协议解析器接口
type ProtocolParser interface {
	// Parse 解析配置字符串为代理节点
	Parse(configLine string) (ProxyNode, error)
	
	// GetProtocol 获取支持的协议名称
	GetProtocol() string
	
	// CanParse 检查是否能解析指定的配置字符串
	CanParse(configLine string) bool
}

// FormatParser 格式解析器接口，用于特定协议的不同格式
type FormatParser interface {
	// Parse 解析特定格式的配置数据
	Parse(configData string) (*VmessNode, error)
	
	// GetFormatName 获取格式名称
	GetFormatName() string
	
	// CanParse 检查是否能解析指定格式的数据
	CanParse(configData string) bool
}

// ProxyInterface 代理接口定义
type ProxyInterface interface {
	// ImportSubscription 导入机场订阅链接
	ImportSubscription(subscriptionURL string) error
	
	// GetRandomProxy 获取随机可用代理
	GetRandomProxy() (*VmessNode, error)
	
	// HealthCheck 执行节点健康检查
	HealthCheck(node *VmessNode) error
	
	// GetAllNodes 获取所有节点
	GetAllNodes() []*VmessNode
	
	// GetHealthyNodes 获取所有健康节点
	GetHealthyNodes() []*VmessNode
}

// SSRProxy 保持原有接口兼容性
type SSRProxy interface {
}

// GenerateNodeID 生成节点唯一标识
func GenerateNodeID(host string, port int) string {
	return fmt.Sprintf("%s:%d", host, port)
}

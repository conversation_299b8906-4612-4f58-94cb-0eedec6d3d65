# 代理接口系统

这是一个功能完整的代理管理系统，支持机场订阅链接导入、随机代理获取和节点健康检查功能。

## 功能特性

### 🚀 核心功能

1. **机场链接导入接口** - 支持从订阅链接获取并解析base64编码的代理配置
2. **随机代理获取接口** - 从已解析的代理列表中智能选择可用节点
3. **节点健康检查接口** - 实时检测代理节点的连通性和响应速度

### 📋 支持的协议格式

当前版本支持 **vmess协议** 的三种主流格式：

- **V2RayN格式**：`vmess://(Base64编码的json格式服务器数据)`
- **ShadowRocket格式**：`vmess://base64([cipher]:[uuid]@[host]:[port])?remarks=queryEscape([name])`
- **Quantumult格式**：`vmess=[host]:[port], method=[method], password=[uuid], tag=[tag]`

## 快速开始

### 基本使用

```go
package main

import (
    "fmt"
    "log"
    "crawler/pkg/proxy"
)

func main() {
    // 创建代理管理器
    pm := proxy.NewProxyManager()
    
    // 导入机场订阅链接
    err := pm.ImportSubscription("https://your-subscription-url")
    if err != nil {
        log.Fatal("导入订阅失败:", err)
    }
    
    fmt.Printf("成功导入 %d 个节点\n", pm.GetNodeCount())
    
    // 执行批量健康检查
    pm.BatchHealthCheck()
    
    // 获取随机可用代理
    node, err := pm.GetRandomProxy()
    if err != nil {
        log.Fatal("获取代理失败:", err)
    }
    
    fmt.Printf("选中代理: %s\n", node.String())
}
```

### 高级功能

```go
// 获取所有健康节点
healthyNodes := pm.GetHealthyNodes()
fmt.Printf("健康节点数量: %d\n", len(healthyNodes))

// 单个节点健康检查
err := pm.HealthCheck(node)
if err != nil {
    fmt.Printf("节点不健康: %v\n", err)
}

// 移除连续失败超过3次的节点
removedCount := pm.RemoveUnhealthyNodes(3)
fmt.Printf("移除了 %d 个不健康节点\n", removedCount)

// 获取节点统计信息
fmt.Printf("总节点数: %d, 健康节点数: %d\n", 
    pm.GetNodeCount(), pm.GetHealthyNodeCount())
```

## API 文档

### ProxyManager 方法

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `NewProxyManager()` | 创建新的代理管理器 | `*ProxyManager` |
| `ImportSubscription(url string)` | 导入机场订阅链接 | `error` |
| `GetRandomProxy()` | 获取随机可用代理 | `(*VmessNode, error)` |
| `HealthCheck(node *VmessNode)` | 执行节点健康检查 | `error` |
| `BatchHealthCheck()` | 批量健康检查所有节点 | `void` |
| `GetAllNodes()` | 获取所有节点 | `[]*VmessNode` |
| `GetHealthyNodes()` | 获取所有健康节点 | `[]*VmessNode` |
| `GetNodeCount()` | 获取节点总数 | `int` |
| `GetHealthyNodeCount()` | 获取健康节点数量 | `int` |
| `RemoveUnhealthyNodes(maxFailCount int)` | 移除不健康节点 | `int` |

### VmessNode 结构体

```go
type VmessNode struct {
    ID       string        // 节点唯一标识
    Name     string        // 节点名称
    Host     string        // 服务器地址
    Port     int           // 端口号
    UUID     string        // 用户ID
    AlterID  int           // 额外ID
    Security string        // 加密方式
    Network  string        // 传输协议
    Type     string        // 伪装类型
    Path     string        // 路径
    TLS      string        // TLS设置
    
    // 健康检查相关
    IsHealthy    bool          // 是否健康
    LastCheck    time.Time     // 最后检查时间
    ResponseTime time.Duration // 响应时间
    FailCount    int           // 连续失败次数
}
```

## 运行测试

```bash
# 运行所有测试
go test ./pkg/proxy -v

# 运行示例程序
go run examples/proxy_demo.go
```

## 配置格式示例

### V2RayN格式示例
```json
{
  "v": "2",
  "ps": "香港节点-01",
  "add": "hk1.example.com",
  "port": "443",
  "id": "12345678-1234-1234-1234-123456789abc",
  "aid": "0",
  "net": "ws",
  "type": "none",
  "host": "hk1.example.com",
  "path": "/path",
  "tls": "tls"
}
```

### ShadowRocket格式示例
```
vmess://YXV0bzoxMjM0NTY3OC0xMjM0LTEyMzQtMTIzNC0xMjM0NTY3ODlhYmNAaGsxLmV4YW1wbGUuY29tOjQ0Mw==?remarks=%E9%A6%99%E6%B8%AF%E8%8A%82%E7%82%B9-01
```

### Quantumult格式示例
```
vmess=hk1.example.com:443, method=auto, password=12345678-1234-1234-1234-123456789abc, tag=香港节点-01
```

## 注意事项

1. **网络连接**：健康检查需要网络连接，请确保网络环境正常
2. **并发安全**：所有方法都是并发安全的，使用了读写锁保护
3. **错误处理**：建议在生产环境中添加适当的错误处理和日志记录
4. **扩展性**：系统设计支持后续添加其他协议（如trojan、ss等）

## 后续计划

- [ ] 支持 Trojan 协议
- [ ] 支持 Shadowsocks 协议
- [ ] 添加配置文件支持
- [ ] 实现代理池负载均衡
- [ ] 添加节点地理位置信息
- [ ] 支持自定义健康检查策略

## 许可证

MIT License

package vmess

import (
	"encoding/base64"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"crawler/pkg/proxy"
)

// ShadowRocketParser ShadowRocket格式解析器
// 格式: vmess://base64([cipher]:[uuid]@[host]:[port])?remarks=queryEscape([name])
type ShadowRocketParser struct{}

// Parse 解析ShadowRocket格式配置
func (p *ShadowRocketParser) Parse(configData string) (*proxy.VmessNode, error) {
	// 检查是否包含查询参数
	parts := strings.Split(configData, "?")
	if len(parts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式不正确，缺少查询参数")
	}

	// Base64解码主要部分
	decodedData, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式Base64解码失败: %v", err)
	}

	// 解析主要部分: [cipher]:[uuid]@[host]:[port]
	configStr := string(decodedData)
	atIndex := strings.LastIndex(configStr, "@")
	if atIndex == -1 {
		return nil, fmt.Errorf("ShadowRocket格式解析失败: 缺少@符号")
	}

	// 分离认证信息和服务器信息
	authPart := configStr[:atIndex]
	serverPart := configStr[atIndex+1:]

	// 解析认证信息: [cipher]:[uuid]
	authParts := strings.Split(authPart, ":")
	if len(authParts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式认证信息不正确")
	}
	cipher := authParts[0]
	uuid := authParts[1]

	// 验证UUID格式（基本检查）
	if len(uuid) < 32 {
		return nil, fmt.Errorf("ShadowRocket格式UUID格式不正确")
	}

	// 解析服务器信息: [host]:[port]
	serverParts := strings.Split(serverPart, ":")
	if len(serverParts) != 2 {
		return nil, fmt.Errorf("ShadowRocket格式服务器信息不正确")
	}
	host := serverParts[0]
	port, err := strconv.Atoi(serverParts[1])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式端口解析失败: %v", err)
	}

	// 验证主机和端口
	if host == "" {
		return nil, fmt.Errorf("ShadowRocket格式主机地址为空")
	}
	if port <= 0 || port > 65535 {
		return nil, fmt.Errorf("ShadowRocket格式端口号无效: %d", port)
	}

	// 解析查询参数
	queryParams, err := url.ParseQuery(parts[1])
	if err != nil {
		return nil, fmt.Errorf("ShadowRocket格式查询参数解析失败: %v", err)
	}

	// 获取节点名称
	name := queryParams.Get("remarks")
	if name == "" {
		name = fmt.Sprintf("%s:%d", host, port)
	}

	// 设置默认值
	security := cipher
	if security == "" {
		security = "auto"
	}

	// 创建节点
	node := CreateVmessNode(
		proxy.GenerateNodeID(host, port),
		name,
		host,
		port,
		uuid,
		0, // ShadowRocket格式通常不使用AlterID
		security,
		"tcp", // ShadowRocket格式默认使用tcp
		"none",
		"",
		"",
	)

	return node, nil
}

// GetFormatName 获取格式名称
func (p *ShadowRocketParser) GetFormatName() string {
	return "ShadowRocket"
}

// CanParse 检查是否能解析指定格式的数据
func (p *ShadowRocketParser) CanParse(configData string) bool {
	// 检查是否包含查询参数
	parts := strings.Split(configData, "?")
	if len(parts) != 2 {
		return false
	}

	// 尝试Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(parts[0])
	if err != nil {
		return false
	}

	// 检查格式: [cipher]:[uuid]@[host]:[port]
	configStr := string(decodedData)
	if !strings.Contains(configStr, "@") || !strings.Contains(configStr, ":") {
		return false
	}

	// 检查查询参数是否可解析
	_, err = url.ParseQuery(parts[1])
	return err == nil
}

package vmess

import (
	"fmt"
	"strconv"
	"strings"

	"crawler/pkg/proxy"
)

// QuantumultParser Quantumult格式解析器
// 格式: vmess=[host]:[port], method=[method], password=[uuid], tag=[tag]
type QuantumultParser struct{}

// Parse 解析Quantumult格式配置
func (p *QuantumultParser) Parse(configData string) (*proxy.VmessNode, error) {
	// 检查是否包含必要的字段
	if !strings.Contains(configData, "vmess=") {
		return nil, fmt.Errorf("不是Quantumult格式，缺少vmess=字段")
	}

	// 解析配置参数
	params := make(map[string]string)
	parts := strings.Split(configData, ",")

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "=") {
			kv := strings.SplitN(part, "=", 2)
			if len(kv) == 2 {
				key := strings.TrimSpace(kv[0])
				value := strings.TrimSpace(kv[1])
				params[key] = value
			}
		}
	}

	// 解析服务器地址和端口
	serverInfo := params["vmess"]
	if serverInfo == "" {
		return nil, fmt.Errorf("Quantumult格式缺少服务器信息")
	}

	serverParts := strings.Split(serverInfo, ":")
	if len(serverParts) != 2 {
		return nil, fmt.Errorf("Quantumult格式服务器信息不正确，期望格式: host:port")
	}

	host := strings.TrimSpace(serverParts[0])
	if host == "" {
		return nil, fmt.Errorf("Quantumult格式主机地址为空")
	}

	port, err := strconv.Atoi(strings.TrimSpace(serverParts[1]))
	if err != nil {
		return nil, fmt.Errorf("Quantumult格式端口解析失败: %v", err)
	}

	if port <= 0 || port > 65535 {
		return nil, fmt.Errorf("Quantumult格式端口号无效: %d", port)
	}

	// 获取UUID（密码）
	uuid := params["password"]
	if uuid == "" {
		return nil, fmt.Errorf("Quantumult格式缺少密码字段")
	}

	// 验证UUID格式（基本检查）
	if len(uuid) < 32 {
		return nil, fmt.Errorf("Quantumult格式UUID格式不正确")
	}

	// 获取加密方法
	method := params["method"]
	if method == "" {
		method = "auto" // 默认加密方法
	}

	// 获取标签（节点名称）
	tag := params["tag"]
	if tag == "" {
		tag = fmt.Sprintf("%s:%d", host, port) // 默认使用host:port作为名称
	}

	// 获取其他可选参数
	network := params["network"]
	if network == "" {
		network = "tcp" // 默认传输协议
	}

	obfs := params["obfs"]
	if obfs == "" {
		obfs = "none" // 默认伪装类型
	}

	path := params["path"]
	if path == "" {
		path = "" // 默认路径为空
	}

	tls := params["tls"]
	if tls == "" {
		tls = "" // 默认不使用TLS
	}

	// 创建节点
	node := CreateVmessNode(
		proxy.GenerateNodeID(host, port),
		tag,
		host,
		port,
		uuid,
		0, // Quantumult格式通常不使用AlterID
		method,
		network,
		obfs,
		path,
		tls,
	)

	return node, nil
}

// GetFormatName 获取格式名称
func (p *QuantumultParser) GetFormatName() string {
	return "Quantumult"
}

// CanParse 检查是否能解析指定格式的数据
func (p *QuantumultParser) CanParse(configData string) bool {
	// 检查必要字段
	if !strings.Contains(configData, "vmess=") {
		return false
	}

	if !strings.Contains(configData, "password=") {
		return false
	}

	// 检查是否为键值对格式
	parts := strings.Split(configData, ",")
	if len(parts) < 2 {
		return false
	}

	// 验证每个部分都包含等号
	validParts := 0
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.Contains(part, "=") {
			validParts++
		}
	}

	// 至少需要有vmess和password两个有效字段
	return validParts >= 2
}

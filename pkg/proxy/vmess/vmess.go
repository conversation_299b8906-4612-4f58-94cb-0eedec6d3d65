// Package vmess 提供vmess协议的解析和处理功能
package vmess

import (
	"fmt"
	"strings"
	"time"

	"crawler/pkg/proxy"
)

// VmessParser vmess协议解析器
type VmessParser struct {
	formatParsers []proxy.FormatParser // 支持的格式解析器列表
}

// NewVmessParser 创建新的vmess协议解析器
func NewVmessParser() *VmessParser {
	parser := &VmessParser{
		formatParsers: make([]proxy.FormatParser, 0),
	}
	
	// 注册支持的格式解析器
	parser.RegisterFormatParser(&V2RayNParser{})
	parser.RegisterFormatParser(&ShadowRocketParser{})
	parser.RegisterFormatParser(&QuantumultParser{})
	
	return parser
}

// RegisterFormatParser 注册格式解析器
func (p *VmessParser) RegisterFormatParser(parser proxy.FormatParser) {
	p.formatParsers = append(p.formatParsers, parser)
}

// Parse 解析vmess配置字符串
func (p *VmessParser) Parse(configLine string) (proxy.ProxyNode, error) {
	configLine = strings.TrimSpace(configLine)
	
	// 检查是否为vmess协议
	if !strings.HasPrefix(configLine, "vmess://") {
		return nil, fmt.Errorf("不支持的协议类型，期望vmess://")
	}
	
	// 移除协议前缀
	configData := strings.TrimPrefix(configLine, "vmess://")
	
	// 尝试使用不同的格式解析器
	var lastErr error
	for _, formatParser := range p.formatParsers {
		if formatParser.CanParse(configData) {
			node, err := formatParser.Parse(configData)
			if err == nil {
				return node, nil
			}
			lastErr = err
		}
	}
	
	// 如果所有格式解析器都失败，尝试逐个解析
	for _, formatParser := range p.formatParsers {
		node, err := formatParser.Parse(configData)
		if err == nil {
			return node, nil
		}
		lastErr = err
	}
	
	if lastErr != nil {
		return nil, fmt.Errorf("无法解析vmess配置格式: %v", lastErr)
	}
	
	return nil, fmt.Errorf("无法解析vmess配置格式")
}

// GetProtocol 获取支持的协议名称
func (p *VmessParser) GetProtocol() string {
	return "vmess"
}

// CanParse 检查是否能解析指定的配置字符串
func (p *VmessParser) CanParse(configLine string) bool {
	return strings.HasPrefix(strings.TrimSpace(configLine), "vmess://")
}

// CreateVmessNode 创建vmess节点的辅助函数
func CreateVmessNode(id, name, host string, port int, uuid string, alterID int, 
	security, network, nodeType, path, tls string) *proxy.VmessNode {
	
	return &proxy.VmessNode{
		ID:              id,
		Name:            name,
		Host:            host,
		Port:            port,
		UUID:            uuid,
		AlterID:         alterID,
		Security:        security,
		Network:         network,
		Type:            nodeType,
		Path:            path,
		TLS:             tls,
		IsHealthyStatus: false, // 初始状态为未检查
		LastCheck:       time.Time{},
		FailCount:       0,
	}
}

// GetSupportedFormats 获取支持的格式列表
func (p *VmessParser) GetSupportedFormats() []string {
	formats := make([]string, 0, len(p.formatParsers))
	for _, parser := range p.formatParsers {
		formats = append(formats, parser.GetFormatName())
	}
	return formats
}

package vmess

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"

	"crawler/pkg/proxy"
)

// V2RayNParser V2RayN格式解析器
// 格式: vmess://(Base64编码的json格式服务器数据)
type V2RayNParser struct{}

// v2rayNConfig V2RayN配置结构
type v2rayNConfig struct {
	V    string `json:"v"`    // 版本
	PS   string `json:"ps"`   // 备注名称
	Add  string `json:"add"`  // 服务器地址
	Port string `json:"port"` // 端口
	ID   string `json:"id"`   // UUID
	Aid  string `json:"aid"`  // 额外ID
	Net  string `json:"net"`  // 传输协议
	Type string `json:"type"` // 伪装类型
	Host string `json:"host"` // 伪装域名
	Path string `json:"path"` // 路径
	TLS  string `json:"tls"`  // TLS
}

// Parse 解析V2RayN格式配置
func (p *V2RayNParser) Parse(configData string) (*proxy.VmessNode, error) {
	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(configData)
	if err != nil {
		return nil, fmt.Errorf("V2RayN格式Base64解码失败: %v", err)
	}

	// JSON解析
	var config v2rayNConfig
	if err := json.Unmarshal(decodedData, &config); err != nil {
		return nil, fmt.Errorf("V2RayN格式JSON解析失败: %v", err)
	}

	// 验证必要字段
	if config.Add == "" {
		return nil, fmt.Errorf("V2RayN格式缺少服务器地址")
	}
	if config.Port == "" {
		return nil, fmt.Errorf("V2RayN格式缺少端口")
	}
	if config.ID == "" {
		return nil, fmt.Errorf("V2RayN格式缺少UUID")
	}

	// 转换端口为整数
	port, err := strconv.Atoi(config.Port)
	if err != nil {
		return nil, fmt.Errorf("V2RayN格式端口格式错误: %v", err)
	}

	// 转换额外ID为整数
	alterID, err := strconv.Atoi(config.Aid)
	if err != nil {
		alterID = 0 // 默认值
	}

	// 设置默认值
	name := config.PS
	if name == "" {
		name = fmt.Sprintf("%s:%d", config.Add, port)
	}

	security := "auto" // V2RayN格式通常使用auto
	network := config.Net
	if network == "" {
		network = "tcp" // 默认传输协议
	}

	nodeType := config.Type
	if nodeType == "" {
		nodeType = "none" // 默认伪装类型
	}

	// 创建节点
	node := CreateVmessNode(
		proxy.GenerateNodeID(config.Add, port),
		name,
		config.Add,
		port,
		config.ID,
		alterID,
		security,
		network,
		nodeType,
		config.Path,
		config.TLS,
	)

	return node, nil
}

// GetFormatName 获取格式名称
func (p *V2RayNParser) GetFormatName() string {
	return "V2RayN"
}

// CanParse 检查是否能解析指定格式的数据
func (p *V2RayNParser) CanParse(configData string) bool {
	// 尝试Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(configData)
	if err != nil {
		return false
	}

	// 尝试JSON解析
	var config v2rayNConfig
	if err := json.Unmarshal(decodedData, &config); err != nil {
		return false
	}

	// 检查是否包含必要的V2RayN字段
	return config.Add != "" && config.Port != "" && config.ID != ""
}

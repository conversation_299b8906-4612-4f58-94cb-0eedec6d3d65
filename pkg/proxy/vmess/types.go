package vmess

import (
	"fmt"
	"time"
)

// VmessNode 代表一个vmess代理节点
type VmessNode struct {
	ID       string `json:"id"`       // 节点唯一标识
	Name     string `json:"name"`     // 节点名称
	Host     string `json:"host"`     // 服务器地址
	Port     int    `json:"port"`     // 端口号
	UUID     string `json:"uuid"`     // 用户ID
	AlterID  int    `json:"alter_id"` // 额外ID
	Security string `json:"security"` // 加密方式
	Network  string `json:"network"`  // 传输协议
	Type     string `json:"type"`     // 伪装类型
	Path     string `json:"path"`     // 路径
	TLS      string `json:"tls"`      // TLS设置
	
	// 健康检查相关
	IsHealthyStatus bool          `json:"is_healthy"`    // 是否健康
	LastCheck       time.Time     `json:"last_check"`    // 最后检查时间
	ResponseTime    time.Duration `json:"response_time"` // 响应时间
	FailCount       int           `json:"fail_count"`    // 连续失败次数
}

// String 返回节点的字符串表示
func (node *VmessNode) String() string {
	status := "不健康"
	if node.IsHealthyStatus {
		status = "健康"
	}
	uuidDisplay := node.UUID
	if len(node.UUID) > 8 {
		uuidDisplay = node.UUID[:8] + "..."
	}
	return fmt.Sprintf("节点[%s] %s:%d (%s) - %s",
		node.Name, node.Host, node.Port, uuidDisplay, status)
}

// GetID 获取节点唯一标识
func (node *VmessNode) GetID() string {
	return node.ID
}

// GetName 获取节点名称
func (node *VmessNode) GetName() string {
	return node.Name
}

// GetHost 获取服务器地址
func (node *VmessNode) GetHost() string {
	return node.Host
}

// GetPort 获取端口号
func (node *VmessNode) GetPort() int {
	return node.Port
}

// GetProtocol 获取协议类型
func (node *VmessNode) GetProtocol() string {
	return "vmess"
}

// IsHealthy 检查节点是否健康
func (node *VmessNode) IsHealthy() bool {
	return node.IsHealthyStatus
}

// GetLastCheck 获取最后检查时间
func (node *VmessNode) GetLastCheck() time.Time {
	return node.LastCheck
}

// GetResponseTime 获取响应时间
func (node *VmessNode) GetResponseTime() time.Duration {
	return node.ResponseTime
}

// GetFailCount 获取连续失败次数
func (node *VmessNode) GetFailCount() int {
	return node.FailCount
}

// FormatParser 格式解析器接口，用于特定协议的不同格式
type FormatParser interface {
	// Parse 解析特定格式的配置数据
	Parse(configData string) (*VmessNode, error)
	
	// GetFormatName 获取格式名称
	GetFormatName() string
	
	// CanParse 检查是否能解析指定格式的数据
	CanParse(configData string) bool
}

// GenerateNodeID 生成节点唯一标识
func GenerateNodeID(host string, port int) string {
	return fmt.Sprintf("%s:%d", host, port)
}

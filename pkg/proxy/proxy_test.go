package proxy

import (
	"encoding/base64"
	"encoding/json"
	"strings"
	"testing"
)

// TestNewProxyManager 测试代理管理器创建
func TestNewProxyManager(t *testing.T) {
	pm := NewProxyManager()
	if pm == nil {
		t.Fatal("代理管理器创建失败")
	}

	if pm.GetNodeCount() != 0 {
		t.<PERSON><PERSON><PERSON>("期望节点数量为0，实际为%d", pm.GetNodeCount())
	}
}

// TestParseV2RayNFormat 测试V2RayN格式解析
func TestParseV2RayNFormat(t *testing.T) {
	pm := NewProxyManager()

	// 创建测试用的V2RayN配置
	v2rayConfig := map[string]interface{}{
		"v":    "2",
		"ps":   "测试节点",
		"add":  "example.com",
		"port": "443",
		"id":   "12345678-1234-1234-1234-123456789abc",
		"aid":  "0",
		"net":  "ws",
		"type": "none",
		"host": "example.com",
		"path": "/path",
		"tls":  "tls",
	}

	// 转换为JSON并Base64编码
	jsonData, err := json.Marshal(v2rayConfig)
	if err != nil {
		t.Fatal("JSON编码失败:", err)
	}

	base64Data := base64.StdEncoding.EncodeToString(jsonData)

	// 测试解析
	node, err := pm.parseV2RayNFormat(base64Data)
	if err != nil {
		t.Fatal("V2RayN格式解析失败:", err)
	}

	// 验证解析结果
	if node.Name != "测试节点" {
		t.Errorf("期望节点名称为'测试节点'，实际为'%s'", node.Name)
	}

	if node.Host != "example.com" {
		t.Errorf("期望主机为'example.com'，实际为'%s'", node.Host)
	}

	if node.Port != 443 {
		t.Errorf("期望端口为443，实际为%d", node.Port)
	}

	if node.UUID != "12345678-1234-1234-1234-123456789abc" {
		t.Errorf("期望UUID为'12345678-1234-1234-1234-123456789abc'，实际为'%s'", node.UUID)
	}
}

// TestParseShadowRocketFormat 测试ShadowRocket格式解析
func TestParseShadowRocketFormat(t *testing.T) {
	pm := NewProxyManager()

	// 创建测试用的ShadowRocket配置
	authInfo := "auto:<EMAIL>:443"
	base64Data := base64.StdEncoding.EncodeToString([]byte(authInfo))
	configData := base64Data + "?remarks=测试节点"

	// 测试解析
	node, err := pm.parseShadowRocketFormat(configData)
	if err != nil {
		t.Fatal("ShadowRocket格式解析失败:", err)
	}

	// 验证解析结果
	if node.Name != "测试节点" {
		t.Errorf("期望节点名称为'测试节点'，实际为'%s'", node.Name)
	}

	if node.Host != "example.com" {
		t.Errorf("期望主机为'example.com'，实际为'%s'", node.Host)
	}

	if node.Port != 443 {
		t.Errorf("期望端口为443，实际为%d", node.Port)
	}
}

// TestParseQuantumultFormat 测试Quantumult格式解析
func TestParseQuantumultFormat(t *testing.T) {
	pm := NewProxyManager()

	// 创建测试用的Quantumult配置
	configData := "vmess=example.com:443, method=auto, password=12345678-1234-1234-1234-123456789abc, tag=测试节点"

	// 测试解析
	node, err := pm.parseQuantumultFormat(configData)
	if err != nil {
		t.Fatal("Quantumult格式解析失败:", err)
	}

	// 验证解析结果
	if node.Name != "测试节点" {
		t.Errorf("期望节点名称为'测试节点'，实际为'%s'", node.Name)
	}

	if node.Host != "example.com" {
		t.Errorf("期望主机为'example.com'，实际为'%s'", node.Host)
	}

	if node.Port != 443 {
		t.Errorf("期望端口为443，实际为%d", node.Port)
	}
}

// TestHealthCheck 测试健康检查功能
func TestHealthCheck(t *testing.T) {
	pm := NewProxyManager()

	// 创建测试节点
	node := &VmessNode{
		ID:   "test:80",
		Name: "测试节点",
		Host: "127.0.0.1", // 使用本地地址进行测试
		Port: 80,
		UUID: "12345678-1234-1234-1234-123456789abc",
	}

	// 执行健康检查（预期会失败，因为没有实际的代理服务）
	err := pm.HealthCheck(node)
	if err == nil {
		t.Log("健康检查意外成功（可能本地80端口有服务）")
	} else {
		t.Log("健康检查失败（预期结果）:", err)
	}

	// 验证节点状态更新
	if node.LastCheck.IsZero() {
		t.Error("最后检查时间未更新")
	}
}

// TestGetRandomProxy 测试随机代理获取
func TestGetRandomProxy(t *testing.T) {
	pm := NewProxyManager()

	// 测试空节点列表
	_, err := pm.GetRandomProxy()
	if err == nil {
		t.Error("期望在空节点列表时返回错误")
	}

	// 添加测试节点
	pm.nodes = []*VmessNode{
		{
			ID:        "test1:443",
			Name:      "测试节点1",
			Host:      "example1.com",
			Port:      443,
			IsHealthy: true,
		},
		{
			ID:        "test2:443",
			Name:      "测试节点2",
			Host:      "example2.com",
			Port:      443,
			IsHealthy: false,
		},
	}

	// 测试获取随机代理
	node, err := pm.GetRandomProxy()
	if err != nil {
		t.Fatal("获取随机代理失败:", err)
	}

	if node == nil {
		t.Fatal("返回的节点为空")
	}

	// 由于优先选择健康节点，应该返回第一个节点
	if node.Name != "测试节点1" {
		t.Errorf("期望返回健康节点'测试节点1'，实际返回'%s'", node.Name)
	}
}

// TestNodeString 测试节点字符串表示
func TestNodeString(t *testing.T) {
	node := &VmessNode{
		ID:        "test:443",
		Name:      "测试节点",
		Host:      "example.com",
		Port:      443,
		UUID:      "12345678-1234-1234-1234-123456789abc",
		IsHealthy: true,
	}

	str := node.String()
	if !strings.Contains(str, "测试节点") {
		t.Error("节点字符串表示应包含节点名称")
	}

	if !strings.Contains(str, "example.com:443") {
		t.Error("节点字符串表示应包含主机和端口")
	}

	if !strings.Contains(str, "健康") {
		t.Error("节点字符串表示应包含健康状态")
	}
}

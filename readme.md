crawler/
├── cmd/                    # 主程序入口
│   └── crawler/
│       └── main.go
├── configs/                # 配置文件目录
│   ├── config.yaml        # 主配置文件
│   └── proxy.yaml         # 代理配置文件
├── internal/              # 内部包
│   ├── core/             # 核心功能
│   │   ├── crawler/      # 爬虫核心
│   │   ├── proxy/        # 代理管理
│   │   ├── scheduler/    # 调度器
│   │   └── parser/       # 解析器
│   ├── middleware/       # 中间件
│   │   ├── limiter/      # 限流器
│   │   ├── logger/       # 日志处理
│   │   └── monitor/      # 监控系统
│   ├── storage/          # 存储相关
│   │   ├── database/     # 数据库操作
│   │   └── queue/        # 消息队列
│   └── utils/            # 工具函数
├── pkg/                   # 可供外部使用的包
│   ├── pool/             # 协程池
│   ├── proxy/            # 代理工具
│   └── useragent/        # UA管理
├── scripts/              # 脚本文件
│   ├── setup.sh         # 环境设置脚本
│   └── deploy.sh        # 部署脚本
├── test/                 # 测试文件
│   ├── integration/     # 集成测试
│   └── mock/            # 模拟数据
├── web/                  # Web界面相关
│   ├── api/             # API接口
│   └── dashboard/       # 控制面板
├── docs/                 # 文档目录
│   ├── api/             # API文档
│   ├── architecture/    # 架构文档
│   └── guides/          # 使用指南
├── .gitignore
├── go.mod
├── go.sum
├── Dockerfile
├── docker-compose.yml
└── README.md

# 分布式网络爬虫系统

## 项目简介
高性能分布式网络爬虫系统，支持多协议代理、智能限流、高并发爬取等特性。

## 主要特性
- 多协议代理支持（SS/SSR/VMess）
- 智能限流和反爬虫策略
- 高效协程池
- 分布式架构
- 实时监控和告警
- 数据清洗和存储

## 快速开始
1. 环境要求
2. 安装步骤
3. 配置说明
4. 运行示例

## 技术架构
[详细架构文档](./docs/architecture/README.md)

## 使用文档
[使用指南](./docs/guides/README.md)

## 开发文档
[API文档](./docs/api/README.md)

## 贡献指南
[贡献指南](./docs/guides/CONTRIBUTING.md)

## 许可证
MIT License
package main

import (
	"crawler/pkg/proxy"
	"fmt"
	"log"
)

func main() {
	// 创建代理管理器
	pm := proxy.NewProxyManager()

	// 导入机场订阅链接
	err := pm.ImportSubscription("https://onlysub.mjurl.com/api/v1/client/subscribe?token=6574374f99280ca9203974f736ab1935")
	if err != nil {
		log.Fatal("导入订阅失败:", err)
	}

	fmt.Printf("成功导入 %d 个节点\n", pm.GetNodeCount())

	// 执行批量健康检查
	pm.BatchHealthCheck()

	// 获取随机可用代理
	node, err := pm.GetRandomProxy()
	if err != nil {
		log.Fatal("获取代理失败:", err)
	}

	fmt.Printf("选中代理: %s\n", node.String())
}

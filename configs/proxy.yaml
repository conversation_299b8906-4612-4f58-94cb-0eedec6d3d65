# 代理配置文件示例

# 订阅链接配置
subscriptions:
  - name: "机场A"
    url: "https://example-a.com/subscription"
    enabled: true
    update_interval: "24h"

  - name: "机场B"
    url: "https://example-b.com/subscription"
    enabled: false
    update_interval: "12h"

# 健康检查配置
health_check:
  enabled: true
  interval: "5m"           # 检查间隔
  timeout: "5s"            # 连接超时
  max_fail_count: 3        # 最大连续失败次数
  remove_unhealthy: true   # 是否自动移除不健康节点

# 代理选择策略
selection:
  strategy: "random"       # 选择策略: random, round_robin, least_latency
  prefer_healthy: true     # 优先选择健康节点
  max_latency: "1s"       # 最大延迟阈值

# 日志配置
logging:
  level: "info"           # 日志级别: debug, info, warn, error
  enable_proxy_logs: true # 是否启用代理相关日志